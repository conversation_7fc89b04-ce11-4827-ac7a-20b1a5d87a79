import { link } from "@/fields/link";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import type { CollectionConfig } from "payload";

export const Critics: CollectionConfig = {
	slug: "critics",
	labels: {
		singular: "Kritik",
		plural: "Kritiken",
	},
	admin: {
		useAsTitle: "title",
	},
	fields: [
		{
			type: "row",
			fields: [
				{
					name: "title",
					label: "Titel",
					type: "text",
					admin: { width: "50%" },
				},
				{
					name: "subline",
					label: "Untertitel",
					type: "text",
					admin: { width: "50%" },
				},
			],
		},
		{
			type: "row",
			fields: [
				{
					name: "author",
					label: "Autor",
					type: "text",
					admin: { width: "50%" },
				},
				{
					name: "concert",
					type: "relationship",
					label: "Konzert",
					relationTo: "concerts",
					hasMany: false,
					admin: { width: "50%" },
				},
			],
		},
		{
			name: "richText",
			type: "richText",
			label: false,
			editor: lexicalEditor({
				features: ({ rootFeatures }) => rootFeatures,
			}),
		},
		link({ appearances: false }),
	],
	hooks: {
		beforeChange: [
			({ data }) => {
				if (data?.name) {
					return {
						...data,
						letter: data.name[0].toUpperCase(),
					};
				}
				return data;
			},
		],
	},
};
