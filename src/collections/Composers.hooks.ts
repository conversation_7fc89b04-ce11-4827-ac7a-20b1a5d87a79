import type {
	CollectionA<PERSON><PERSON><PERSON>eHook,
	CollectionAfterDeleteHook,
} from "payload";

import { revalidatePath, revalidateTag } from "next/cache";
import type { Composer } from "@/payload-types";

export const revalidateComposer: CollectionAfterChangeHook<Composer> = async ({
	doc,
	req: { payload, context },
}) => {
	if (!context.disableRevalidate) {
		const pages = await payload.find({
			collection: "pages",
			depth: 5,
			limit: 1,
			where: {
				"layout.blockType": {
					equals: "composer-archive",
				},
			},
		});

		for (const page of pages.docs) {
			const path = page.slug === "home" ? "/" : `/${page.slug}`;

			payload.logger.info(`Revalidating page at path: ${path}`);

			revalidatePath(path);
			revalidateTag("pages-sitemap");
		}
	}
	return doc;
};
